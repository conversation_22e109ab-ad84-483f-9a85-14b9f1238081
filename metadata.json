{"macid": 206863099762778, "mac": "BC:24:11:94:18:5A", "model": "Praevista-GeoSCADA-NR2", "label": "CT325", "description": "Praevista Lab", "registers": [{"index": 1, "dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "GTS0", "units": "°C", "display": 2}, {"index": 2, "dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "GTS1", "units": "°C", "display": 2}, {}, {"index": 4, "dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "GPS1", "units": "PSIG", "display": 2}, {}, {}, {}, {}, {"index": 9, "dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "B1TS0", "units": "°C", "display": 2}, {"index": 10, "dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "B1TS1", "units": "°C", "display": 2}, {"index": 11, "dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "A1TS0", "units": "°C", "display": 2}, {"index": 12, "dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "A1TS1", "units": "°C", "display": 2}, {"index": 13, "dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "B2TS0", "units": "°C", "display": 2}, {"index": 14, "dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "B2TS1", "units": "°C", "display": 2}, {"index": 15, "dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "A2TS0", "units": "°C", "display": 2}, {"index": 16, "dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "A2TS1", "units": "°C", "display": 2}, {"index": 17, "dtype": "B", "group": "DigitalIO", "device": "DigitalIO", "register": "GGF", "units": "", "display": null}, {"index": 18, "dtype": "B", "group": "DigitalIO", "device": "DigitalIO", "register": "B1GF", "units": "", "display": null}, {"index": 19, "dtype": "B", "group": "DigitalIO", "device": "DigitalIO", "register": "B2GF", "units": "", "display": null}, {}, {}, {}, {}, {}, {"index": 25, "dtype": "F32", "group": "GP1", "device": "GP1", "register": "ActualSpeedRPM", "units": "RPM", "display": 0}, {"index": 26, "dtype": "F32", "group": "GP1", "device": "GP1", "register": "MotorPower", "units": "kW", "display": 3}, {"index": 27, "dtype": "F32", "group": "GP1", "device": "GP1", "register": "MotorInputVoltage", "units": "V", "display": 1}, {"index": 28, "dtype": "F32", "group": "GP1", "device": "GP1", "register": "MotorInputCurrent", "units": "A", "display": 2}, {"index": 29, "dtype": "F32", "group": "GP1", "device": "GP1", "register": "SensorlessHead", "units": "ft", "display": 2}, {"index": 30, "dtype": "F32", "group": "GP1", "device": "GP1", "register": "SensorlessFlow", "units": "US GPM", "display": 2}, {"index": 31, "dtype": "B", "group": "GP1", "device": "GP1", "register": "Status", "units": "", "display": 0}, {"index": 32, "dtype": "U8", "group": "GP1", "device": "GP1", "register": "HOAState", "units": "", "display": null}, {"index": 33, "dtype": "F32", "group": "GP1", "device": "GP1", "register": "ControlSetpoint", "units": "US GPM", "display": 2}, {}, {}, {}, {"index": 37, "dtype": "F32", "group": "GP2", "device": "GP2", "register": "ActualSpeedRPM", "units": "RPM", "display": 0}, {"index": 38, "dtype": "F32", "group": "GP2", "device": "GP2", "register": "MotorPower", "units": "kW", "display": 3}, {"index": 39, "dtype": "F32", "group": "GP2", "device": "GP2", "register": "MotorInputVoltage", "units": "V", "display": 1}, {"index": 40, "dtype": "F32", "group": "GP2", "device": "GP2", "register": "MotorInputCurrent", "units": "A", "display": 2}, {"index": 41, "dtype": "F32", "group": "GP2", "device": "GP2", "register": "SensorlessHead", "units": "ft", "display": 2}, {"index": 42, "dtype": "F32", "group": "GP2", "device": "GP2", "register": "SensorlessFlow", "units": "US GPM", "display": 2}, {"index": 43, "dtype": "B", "group": "GP2", "device": "GP2", "register": "Status", "units": "", "display": 0}, {"index": 44, "dtype": "U8", "group": "GP2", "device": "GP2", "register": "HOAState", "units": "", "display": null}, {"index": 45, "dtype": "F32", "group": "GP2", "device": "GP2", "register": "ControlSetpoint", "units": "US GPM", "display": 2}, {}, {}, {}, {"index": 49, "dtype": "F32", "group": "geothermal", "device": "geothermal", "register": "HeatTransferRate", "units": "kW", "display": 3}, {"index": 50, "dtype": "F32", "group": "geothermal", "device": "geothermal", "register": "HeatTransferIn", "units": "kWh", "display": 3}, {"index": 51, "dtype": "F32", "group": "geothermal", "device": "geothermal", "register": "HeatTransferOut", "units": "kWh", "display": 3}, {}, {}, {}, {"index": 55, "dtype": "F32", "group": "B1P1", "device": "B1P1", "register": "ActualSpeedRPM", "units": "RPM", "display": 0}, {"index": 56, "dtype": "F32", "group": "B1P1", "device": "B1P1", "register": "MotorPower", "units": "kW", "display": 3}, {"index": 57, "dtype": "F32", "group": "B1P1", "device": "B1P1", "register": "MotorInputVoltage", "units": "V", "display": 1}, {"index": 58, "dtype": "F32", "group": "B1P1", "device": "B1P1", "register": "MotorInputCurrent", "units": "A", "display": 2}, {"index": 59, "dtype": "F32", "group": "B1P1", "device": "B1P1", "register": "SensorlessHead", "units": "ft", "display": 2}, {"index": 60, "dtype": "F32", "group": "B1P1", "device": "B1P1", "register": "SensorlessFlow", "units": "US GPM", "display": 2}, {"index": 61, "dtype": "B", "group": "B1P1", "device": "B1P1", "register": "Status", "units": "", "display": 0}, {"index": 62, "dtype": "U8", "group": "B1P1", "device": "B1P1", "register": "HOAState", "units": "", "display": null}, {"index": 63, "dtype": "F32", "group": "B1P1", "device": "B1P1", "register": "ControlSetpoint", "units": "US GPM", "display": 2}, {}, {}, {}, {"index": 67, "dtype": "F32", "group": "B1P2", "device": "B1P2", "register": "ActualSpeedRPM", "units": "RPM", "display": 0}, {"index": 68, "dtype": "F32", "group": "B1P2", "device": "B1P2", "register": "MotorPower", "units": "kW", "display": 3}, {"index": 69, "dtype": "F32", "group": "B1P2", "device": "B1P2", "register": "MotorInputVoltage", "units": "V", "display": 1}, {"index": 70, "dtype": "F32", "group": "B1P2", "device": "B1P2", "register": "MotorInputCurrent", "units": "A", "display": 2}, {"index": 71, "dtype": "F32", "group": "B1P2", "device": "B1P2", "register": "SensorlessHead", "units": "ft", "display": 2}, {"index": 72, "dtype": "F32", "group": "B1P2", "device": "B1P2", "register": "SensorlessFlow", "units": "US GPM", "display": 2}, {"index": 73, "dtype": "B", "group": "B1P2", "device": "B1P2", "register": "Status", "units": "", "display": 0}, {"index": 74, "dtype": "U8", "group": "B1P2", "device": "B1P2", "register": "HOAState", "units": "", "display": null}, {"index": 75, "dtype": "F32", "group": "B1P2", "device": "B1P2", "register": "ControlSetpoint", "units": "US GPM", "display": 2}, {}, {}, {}, {"index": 79, "dtype": "F32", "group": "building1", "device": "building1", "register": "HeatTransferRate", "units": "kW", "display": 3}, {"index": 80, "dtype": "F32", "group": "building1", "device": "building1", "register": "HeatTransferIn", "units": "kWh", "display": 3}, {"index": 81, "dtype": "F32", "group": "building1", "device": "building1", "register": "HeatTransferOut", "units": "kWh", "display": 3}, {}, {}, {}, {}, {}, {"index": 87, "dtype": "F32", "group": "A1P1", "device": "A1P1", "register": "ActualSpeedRPM", "units": "RPM", "display": 0}, {"index": 88, "dtype": "F32", "group": "A1P1", "device": "A1P1", "register": "MotorPower", "units": "kW", "display": 3}, {"index": 89, "dtype": "F32", "group": "A1P1", "device": "A1P1", "register": "MotorInputVoltage", "units": "V", "display": 1}, {"index": 90, "dtype": "F32", "group": "A1P1", "device": "A1P1", "register": "MotorInputCurrent", "units": "A", "display": 2}, {"index": 91, "dtype": "F32", "group": "A1P1", "device": "A1P1", "register": "SensorlessHead", "units": "ft", "display": 2}, {"index": 92, "dtype": "F32", "group": "A1P1", "device": "A1P1", "register": "SensorlessFlow", "units": "US GPM", "display": 2}, {"index": 93, "dtype": "B", "group": "A1P1", "device": "A1P1", "register": "Status", "units": "", "display": 0}, {"index": 94, "dtype": "U8", "group": "A1P1", "device": "A1P1", "register": "HOAState", "units": "", "display": null}, {"index": 95, "dtype": "F32", "group": "A1P1", "device": "A1P1", "register": "ControlSetpoint", "units": "US GPM", "display": 2}, {}, {}, {}, {"index": 99, "dtype": "F32", "group": "auxiliary1", "device": "auxiliary1", "register": "HeatTransferRate", "units": "kW", "display": 3}, {"index": 100, "dtype": "F32", "group": "auxiliary1", "device": "auxiliary1", "register": "HeatTransferIn", "units": "kWh", "display": 3}, {"index": 101, "dtype": "F32", "group": "auxiliary1", "device": "auxiliary1", "register": "HeatTransferOut", "units": "kWh", "display": 3}, {}, {}, {}, {}, {}, {"index": 107, "dtype": "F32", "group": "B2P1", "device": "B2P1", "register": "ActualSpeedRPM", "units": "RPM", "display": 0}, {"index": 108, "dtype": "F32", "group": "B2P1", "device": "B2P1", "register": "MotorPower", "units": "kW", "display": 3}, {"index": 109, "dtype": "F32", "group": "B2P1", "device": "B2P1", "register": "MotorInputVoltage", "units": "V", "display": 1}, {"index": 110, "dtype": "F32", "group": "B2P1", "device": "B2P1", "register": "MotorInputCurrent", "units": "A", "display": 2}, {"index": 111, "dtype": "F32", "group": "B2P1", "device": "B2P1", "register": "SensorlessHead", "units": "ft", "display": 2}, {"index": 112, "dtype": "F32", "group": "B2P1", "device": "B2P1", "register": "SensorlessFlow", "units": "US GPM", "display": 2}, {"index": 113, "dtype": "B", "group": "B2P1", "device": "B2P1", "register": "Status", "units": "", "display": 0}, {"index": 114, "dtype": "U8", "group": "B2P1", "device": "B2P1", "register": "HOAState", "units": "", "display": null}, {"index": 115, "dtype": "F32", "group": "B2P1", "device": "B2P1", "register": "ControlSetpoint", "units": "US GPM", "display": 2}, {}, {}, {}, {"index": 119, "dtype": "F32", "group": "B2P2", "device": "B2P2", "register": "ActualSpeedRPM", "units": "RPM", "display": 0}, {"index": 120, "dtype": "F32", "group": "B2P2", "device": "B2P2", "register": "MotorPower", "units": "kW", "display": 3}, {"index": 121, "dtype": "F32", "group": "B2P2", "device": "B2P2", "register": "MotorInputVoltage", "units": "V", "display": 1}, {"index": 122, "dtype": "F32", "group": "B2P2", "device": "B2P2", "register": "MotorInputCurrent", "units": "A", "display": 2}, {"index": 123, "dtype": "F32", "group": "B2P2", "device": "B2P2", "register": "SensorlessHead", "units": "ft", "display": 2}, {"index": 124, "dtype": "F32", "group": "B2P2", "device": "B2P2", "register": "SensorlessFlow", "units": "US GPM", "display": 2}, {"index": 125, "dtype": "B", "group": "B2P2", "device": "B2P2", "register": "Status", "units": "", "display": 0}, {"index": 126, "dtype": "U8", "group": "B2P2", "device": "B2P2", "register": "HOAState", "units": "", "display": null}, {"index": 127, "dtype": "F32", "group": "B2P2", "device": "B2P2", "register": "ControlSetpoint", "units": "US GPM", "display": 2}, {}, {}, {}, {"index": 131, "dtype": "F32", "group": "building2", "device": "building2", "register": "HeatTransferRate", "units": "kW", "display": 3}, {"index": 132, "dtype": "F32", "group": "building2", "device": "building2", "register": "HeatTransferIn", "units": "kWh", "display": 3}, {"index": 133, "dtype": "F32", "group": "building2", "device": "building2", "register": "HeatTransferOut", "units": "kWh", "display": 3}, {}, {}, {}, {}, {}, {"index": 139, "dtype": "F32", "group": "A2P1", "device": "A2P1", "register": "ActualSpeedRPM", "units": "RPM", "display": 0}, {"index": 140, "dtype": "F32", "group": "A2P1", "device": "A2P1", "register": "MotorPower", "units": "kW", "display": 3}, {"index": 141, "dtype": "F32", "group": "A2P1", "device": "A2P1", "register": "MotorInputVoltage", "units": "V", "display": 1}, {"index": 142, "dtype": "F32", "group": "A2P1", "device": "A2P1", "register": "MotorInputCurrent", "units": "A", "display": 2}, {"index": 143, "dtype": "F32", "group": "A2P1", "device": "A2P1", "register": "SensorlessHead", "units": "ft", "display": 2}, {"index": 144, "dtype": "F32", "group": "A2P1", "device": "A2P1", "register": "SensorlessFlow", "units": "US GPM", "display": 2}, {"index": 145, "dtype": "B", "group": "A2P1", "device": "A2P1", "register": "Status", "units": "", "display": 0}, {"index": 146, "dtype": "U8", "group": "A2P1", "device": "A2P1", "register": "HOAState", "units": "", "display": null}, {"index": 147, "dtype": "F32", "group": "A2P1", "device": "A2P1", "register": "ControlSetpoint", "units": "US GPM", "display": 2}, {}, {}, {}, {"index": 151, "dtype": "F32", "group": "auxiliary2", "device": "auxiliary2", "register": "HeatTransferRate", "units": "kW", "display": 3}, {"index": 152, "dtype": "F32", "group": "auxiliary2", "device": "auxiliary2", "register": "HeatTransferIn", "units": "kWh", "display": 3}, {"index": 153, "dtype": "F32", "group": "auxiliary2", "device": "auxiliary2", "register": "HeatTransferOut", "units": "kWh", "display": 3}, {}, {}, {}, {}, {}], "trends": [{"title": "Temperature", "values": [{"device": "AnalogIn", "register": "GTS0"}, {"device": "AnalogIn", "register": "GTS1"}, {"device": "AnalogIn", "register": "B1TS0"}, {"device": "AnalogIn", "register": "B1TS1"}, {"device": "AnalogIn", "register": "A1TS0"}, {"device": "AnalogIn", "register": "A1TS1"}, {"device": "AnalogIn", "register": "B2TS0"}, {"device": "AnalogIn", "register": "B2TS1"}, {"device": "AnalogIn", "register": "A2TS0"}, {"device": "AnalogIn", "register": "A2TS1"}]}, {"title": "Pressure", "values": [{"device": "AnalogIn", "register": "GPS1"}]}, {"title": "Heat Transfer Rate", "values": [{"device": "geothermal", "register": "HeatTransferRate"}, {"device": "building1", "register": "HeatTransferRate"}, {"device": "auxiliary1", "register": "HeatTransferRate"}, {"device": "building2", "register": "HeatTransferRate"}, {"device": "auxiliary2", "register": "HeatTransferRate"}]}, {"title": "Heat Transfer (Integrated)", "values": [{"device": "geothermal", "register": "HeatTransferIn", "cumulative": -3600}, {"device": "geothermal", "register": "HeatTransferOut", "cumulative": 3600}, {"device": "building1", "register": "HeatTransferIn", "cumulative": -3600}, {"device": "building1", "register": "HeatTransferOut", "cumulative": 3600}, {"device": "auxiliary1", "register": "HeatTransferIn", "cumulative": -3600}, {"device": "auxiliary1", "register": "HeatTransferOut", "cumulative": 3600}, {"device": "building2", "register": "HeatTransferIn", "cumulative": -3600}, {"device": "building2", "register": "HeatTransferOut", "cumulative": 3600}, {"device": "auxiliary2", "register": "HeatTransferIn", "cumulative": -3600}, {"device": "auxiliary2", "register": "HeatTransferOut", "cumulative": 3600}]}, {"title": "Heat Transfer (Cumulative)", "values": [{"device": "geothermal", "register": "HeatTransferIn"}, {"device": "geothermal", "register": "HeatTransferOut"}, {"device": "building1", "register": "HeatTransferIn"}, {"device": "building1", "register": "HeatTransferOut"}, {"device": "auxiliary1", "register": "HeatTransferIn"}, {"device": "auxiliary1", "register": "HeatTransferOut"}, {"device": "building2", "register": "HeatTransferIn"}, {"device": "building2", "register": "HeatTransferOut"}, {"device": "auxiliary2", "register": "HeatTransferIn"}, {"device": "auxiliary2", "register": "HeatTransferOut"}]}, {"title": "Flow", "values": [{"device": "GP1", "register": "ControlSetpoint"}, {"device": "GP1", "register": "SensorlessFlow"}, {"device": "GP2", "register": "ControlSetpoint"}, {"device": "GP2", "register": "SensorlessFlow"}, {"device": "B1P1", "register": "SensorlessFlow"}, {"device": "B1P2", "register": "SensorlessFlow"}, {"device": "A1P1", "register": "ControlSetpoint"}, {"device": "A1P1", "register": "SensorlessFlow"}, {"device": "B2P1", "register": "SensorlessFlow"}, {"device": "B2P2", "register": "SensorlessFlow"}, {"device": "A2P1", "register": "ControlSetpoint"}, {"device": "A2P1", "register": "SensorlessFlow"}]}, {"title": "Pump Head", "values": [{"device": "GP1", "register": "SensorlessHead"}, {"device": "GP2", "register": "SensorlessHead"}, {"device": "B1P1", "register": "SensorlessHead"}, {"device": "B1P2", "register": "SensorlessHead"}, {"device": "A1P1", "register": "SensorlessHead"}, {"device": "B2P1", "register": "SensorlessHead"}, {"device": "B2P2", "register": "SensorlessHead"}, {"device": "A2P1", "register": "SensorlessHead"}]}, {"title": "Motor RPM", "values": [{"device": "GP1", "register": "ActualSpeedRPM"}, {"device": "GP2", "register": "ActualSpeedRPM"}, {"device": "B1P1", "register": "ActualSpeedRPM"}, {"device": "B1P2", "register": "ActualSpeedRPM"}, {"device": "A1P1", "register": "ActualSpeedRPM"}, {"device": "B2P1", "register": "ActualSpeedRPM"}, {"device": "B2P2", "register": "ActualSpeedRPM"}, {"device": "A2P1", "register": "ActualSpeedRPM"}]}, {"title": "Motor Power", "values": [{"device": "GP1", "register": "MotorPower"}, {"device": "GP2", "register": "MotorPower"}, {"device": "B1P1", "register": "MotorPower"}, {"device": "B1P2", "register": "MotorPower"}, {"device": "A1P1", "register": "MotorPower"}, {"device": "B2P1", "register": "MotorPower"}, {"device": "B2P2", "register": "MotorPower"}, {"device": "A2P1", "register": "MotorPower"}]}, {"title": "Motor Voltage", "values": [{"device": "GP1", "register": "MotorInputVoltage"}, {"device": "GP2", "register": "MotorInputVoltage"}, {"device": "B1P1", "register": "MotorInputVoltage"}, {"device": "B1P2", "register": "MotorInputVoltage"}, {"device": "A1P1", "register": "MotorInputVoltage"}, {"device": "B2P1", "register": "MotorInputVoltage"}, {"device": "B2P2", "register": "MotorInputVoltage"}, {"device": "A2P1", "register": "MotorInputVoltage"}]}, {"title": "Motor Current", "values": [{"device": "GP1", "register": "MotorInputCurrent"}, {"device": "GP2", "register": "MotorInputCurrent"}, {"device": "B1P1", "register": "MotorInputCurrent"}, {"device": "B1P2", "register": "MotorInputCurrent"}, {"device": "A1P1", "register": "MotorInputCurrent"}, {"device": "B2P1", "register": "MotorInputCurrent"}, {"device": "B2P2", "register": "MotorInputCurrent"}, {"device": "A2P1", "register": "MotorInputCurrent"}]}, {"title": "HOA State", "values": [{"device": "GP1", "register": "HOAState"}, {"device": "GP2", "register": "HOAState"}, {"device": "B1P1", "register": "HOAState"}, {"device": "B1P2", "register": "HOAState"}, {"device": "A1P1", "register": "HOAState"}, {"device": "B2P1", "register": "HOAState"}, {"device": "B2P2", "register": "HOAState"}, {"device": "A2P1", "register": "HOAState"}]}, {"title": "Glycol State", "values": [{"device": "DigitalIO", "register": "GGF"}, {"device": "DigitalIO", "register": "B1GF"}, {"device": "DigitalIO", "register": "B2GF"}]}]}