export class DeviceService {
    constructor(knex) {
        this.knex = knex;
    }

    async findDeviceByMac(mac) {
        console.log(`Finding device by MAC: ${mac}`);

        const cleanMac = mac.replace(/:/g, '').toLowerCase();
        let macid;

        try {
            macid = parseInt(cleanMac, 16);

            const hexCheck = macid.toString(16).padStart(12, '0');
            if (hexCheck !== cleanMac) {
                console.warn(`MAC address too large for parseInt, using alternative approach`);
                const macidBigInt = BigInt(`0x${cleanMac}`);
                macid = Number(macidBigInt);
            }
        } catch (error) {
            console.error(`Error converting MAC to integer: ${error.message}`);
            macid = parseInt(cleanMac, 16);
        }

        console.log(`Converted MAC to integer: ${macid} (type: ${typeof macid})`);
        console.log(`Looking up device with macid: ${macid}`);

        // look up device by macid
        const device = await this.knex('devices')
            .where({ macid })
            .first();

        if (device) {
            console.log(`Found device with macid ${macid}`); // : ${JSON.stringify(device)}

            if (device.mac !== mac) {
                console.log(`Updating device MAC from ${device.mac} to ${mac}`);
                await this.knex('devices')
                    .where({ macid })
                    .update({ mac });
            }
        } else {
            console.log(`No device found with macid ${macid}`);
        }
        return device;
    }

    async createDevice(mac, metadata, additionalFields = {}) {
        const now = Math.floor(Date.now() / 1000);

        const cleanMac = mac.replace(/:/g, '').toLowerCase();
        console.log(`Clean MAC: ${cleanMac}`);

        let macid;

        try {
            macid = parseInt(cleanMac, 16);

            const hexCheck = macid.toString(16).padStart(12, '0');
            if (hexCheck !== cleanMac) {
                console.warn(`MAC address too large for parseInt, using alternative approach`);
                const macidBigInt = BigInt(`0x${cleanMac}`);
                macid = Number(macidBigInt);
            }
        } catch (error) {
            console.error(`Error converting MAC to integer: ${error.message}`);
            // fllback to direct conversion
            macid = parseInt(cleanMac, 16);
        }

        console.log(`Converted MAC to integer: ${macid} (type: ${typeof macid})`);

        // verification only - not used for storage
        const verifyHex = macid.toString(16).padStart(12, '0');
        console.log(`Verification - integer back to hex: ${verifyHex}`);
        console.log(`Original clean MAC: ${cleanMac}`);
        console.log(`Conversion match: ${verifyHex === cleanMac ? 'Yes' : 'No'}`);

        if (verifyHex !== cleanMac) {
            console.warn(`MAC conversion mismatch! Original: ${cleanMac}, Converted back: ${verifyHex}`);
            console.warn(`This may indicate precision loss for this MAC address.`);
        }

        try {
            // if a device with this macid already exists
            const existingDevice = await this.knex('devices').where({ macid }).first();

            if (existingDevice) {
                console.log(`Device with macid ${macid} already exists. Updating instead of creating.`);
                console.log(`Existing device: ${JSON.stringify(existingDevice)}`);

                // if the device exists but with a different MAC address, this is a conflict
                if (existingDevice.mac !== mac) {
                    console.error(`MAC address conflict: Device with macid ${macid} exists but has MAC ${existingDevice.mac} instead of ${mac}`);
                    throw new Error(`MAC address conflict: Another device with the same numeric ID but different MAC address already exists`);
                }

                const existingMetadata = existingDevice.meta ? JSON.parse(existingDevice.meta) : null;
                const changes = await this.compareMetadata(existingMetadata, metadata);

                console.log(`Metadata comparison for existing device: ${JSON.stringify(changes)}`);

                const metaHistory = JSON.parse(existingDevice.meta_history || '[]');

                let newVersion = existingDevice.meta_version || 0;
                if (changes.hasChanges) {
                    console.log('Changes detected, incrementing version');
                    newVersion += 1;
                } else {
                    console.log('No changes detected, keeping current version');
                }

                if (changes.hasChanges) {
                    metaHistory.push({
                        version: newVersion,
                        timestamp: now,
                        metadata: metadata
                    });
                }

                const updateObj = {
                    meta_version: newVersion,
                    meta_history: JSON.stringify(metaHistory),
                    meta: JSON.stringify(metadata),
                    utime: now
                };

                updateObj.nchan = metadata.registers ? metadata.registers.length : (metadata.nchan || 0);

                if (metadata.model) {
                    updateObj.model = metadata.model;
                }

                const allowedFields = ['label', 'description', 'longitude', 'latitude'];
                for (const field of allowedFields) {
                    if (metadata[field] !== undefined) {
                        updateObj[field] = metadata[field];
                    }
                }

                for (const [key, value] of Object.entries(additionalFields)) {
                    if (allowedFields.includes(key)) {
                        updateObj[key] = value;
                    }
                }

                await this.knex('devices')
                    .where({ macid })
                    .update(updateObj);

                console.log(`Updated existing device with macid: ${macid}`);
                return [macid]; 
            } else {
                // store metadata in meta_history as the first version
                const metaHistory = [{
                    version: 1,
                    timestamp: now,
                    metadata: metadata
                }];

                // create device with null oid (will be claimed later)
                console.log(`Inserting new device with macid: ${macid}`);
                // prepare insert object 
                const insertObj = {
                    macid,
                    mac,
                    meta_version: 1,
                    meta_history: JSON.stringify(metaHistory),
                    meta: JSON.stringify(metadata), 
                    ctime: now,
                    utime: now,
                    oid: null // (device unclaimed initially)
                };

                insertObj.nchan = metadata.registers ? metadata.registers.length : (metadata.nchan || 0);

                if (metadata.model) {
                    insertObj.model = metadata.model;
                }

                const allowedFields = ['label', 'description', 'longitude', 'latitude'];
                for (const field of allowedFields) {
                    if (metadata[field] !== undefined) {
                        insertObj[field] = metadata[field];
                    }
                }

                for (const [key, value] of Object.entries(additionalFields)) {
                    if (allowedFields.includes(key)) {
                        insertObj[key] = value;
                    }
                }

                return await this.knex('devices').insert(insertObj);
            }
        } catch (error) {
            console.error(`Error creating/updating device: ${error.message}`);
            throw error;
        }
    }

    /**
     * Update device metadata with a specific version number
     * @param {string} mac - Device MAC address
     * @param {object} metadata - New metadata
     * @param {number} newVersion - Version number to set
     * @returns {Promise} - Result of the update operation
     */
    async updateDeviceMetadataWithVersion(mac, metadata, newVersion) {
        const now = Math.floor(Date.now() / 1000);
        const device = await this.findDeviceByMac(mac);

        if (!device) {
            throw new Error('Device not found');
        }

        // get current meta_history and append new version
        const metaHistory = JSON.parse(device.meta_history || '[]');

        metaHistory.push({
            version: newVersion,
            timestamp: now,
            metadata: metadata
        });

        const cleanMac = mac.replace(/:/g, '').toLowerCase();
        let macid;

        try {
            macid = parseInt(cleanMac, 16);

            const hexCheck = macid.toString(16).padStart(12, '0');
            if (hexCheck !== cleanMac) {
                console.warn(`MAC address too large for parseInt, using alternative approach`);
                const macidBigInt = BigInt(`0x${cleanMac}`);
                macid = Number(macidBigInt);
            }
        } catch (error) {
            console.error(`Error converting MAC to integer: ${error.message}`);
            macid = parseInt(cleanMac, 16);
        }

        console.log(`Converted MAC to integer: ${macid} (type: ${typeof macid})`);
        console.log(`Updating device with macid: ${macid}, setting version to ${newVersion}`);

        const updateObj = {
            meta_version: newVersion,
            meta_history: JSON.stringify(metaHistory),
            meta: JSON.stringify(metadata), // update meta field for compatibility
            utime: now
        };

        updateObj.nchan = metadata.registers ? metadata.registers.length : (metadata.nchan || 0);

        if (metadata.model) {
            updateObj.model = metadata.model;
        }

        const allowedFields = ['label', 'description', 'longitude', 'latitude'];
        for (const field of allowedFields) {
            if (metadata[field] !== undefined) {
                updateObj[field] = metadata[field];
            }
        }

        return await this.knex('devices')
            .where({ macid })
            .update(updateObj);
    }

    /**
     * Update device metadata and increment version
     * @param {string} mac - Device MAC address
     * @param {object} metadata - New metadata
     * @returns {Promise} - Result of the update operation
     */
    async updateDeviceMetadata(mac, metadata) {
        const device = await this.findDeviceByMac(mac);
        if (!device) {
            throw new Error('Device not found');
        }

        const newVersion = device.meta_version + 1;
        return this.updateDeviceMetadataWithVersion(mac, metadata, newVersion);
    }

    async getDeviceMetadata(mac) {
        const device = await this.findDeviceByMac(mac);
        if (!device) {
            return null;
        }

        return device.meta ? JSON.parse(device.meta) : null;
    }

    async updateDeviceTimestamps(mac, { stime, rtime, utime }) {
        const updates = {};
        if (stime) updates.stime = stime;
        if (rtime) updates.rtime = rtime;
        if (utime) updates.utime = utime;

        const cleanMac = mac.replace(/:/g, '').toLowerCase();
        let macid;

        try {
            macid = parseInt(cleanMac, 16);

            const hexCheck = macid.toString(16).padStart(12, '0');
            if (hexCheck !== cleanMac) {
                console.warn(`MAC address too large for parseInt, using alternative approach`);
                const macidBigInt = BigInt(`0x${cleanMac}`);
                macid = Number(macidBigInt);
            }
        } catch (error) {
            console.error(`Error converting MAC to integer: ${error.message}`);
            // fallback to direct conversion
            macid = parseInt(cleanMac, 16);
        }

        console.log(`Converted MAC to integer: ${macid} (type: ${typeof macid})`);
        console.log(`Updating timestamps for device with macid: ${macid}`);

        return await this.knex('devices')
            .where({ macid })
            .update(updates);
    }

    async compareMetadata(currentMetadata, newMetadata) {
        if (!currentMetadata) {
            // First time metadata is set - counts as change
            return {
                type: 'COMPATIBLE',
                changes: [],
                migrationNeeded: false,
                hasChanges: true 
            };
        }

        const changes = [];
        let migrationNeeded = false;
        let migrationStrategy = null;
        let hasChanges = false; // tacks actual changes

        // comparing registers
        const currentRegs = currentMetadata.registers || [];
        const newRegs = newMetadata.registers || [];

        // maps of registers (register identifier)
        const currentRegMap = new Map(currentRegs.map((reg, idx) => [reg.register || reg.name, { ...reg, index: idx }]));
        const newRegMap = new Map(newRegs.map((reg, idx) => [reg.register || reg.name, { ...reg, index: idx }]));

        // find signals with changed dtypes first
        const changedDtypeRegisters = newRegs.filter(newReg => {
            const regId = newReg.register || newReg.name;
            if (!regId) return false; // registers without identifiers

            const currentReg = currentRegMap.get(regId);

            return currentReg &&
                currentReg.dtype !== undefined &&
                newReg.dtype !== undefined &&
                currentReg.dtype.toLowerCase() !== newReg.dtype.toLowerCase();
        });

        // register IDs with dtype changes (avoid double-counting)
        const changedDtypeIds = new Set(
            changedDtypeRegisters.map(reg => reg.register || reg.name)
        );

        // find added registers (excluding those with dtype changes)
        const addedRegisters = newRegs.filter(reg => {
            const regId = reg.register || reg.name;
            if (!regId) return false; // Skip registers without identifiers

            return !currentRegMap.has(regId) && !changedDtypeIds.has(regId);
        });

        // find removed registers
        const removedRegisters = currentRegs.filter(reg => {
            const regId = reg.register || reg.name;
            if (!regId) return false; 

            return !newRegMap.has(regId);
        });

        // model changes (default benign)
        if (currentMetadata.model !== newMetadata.model) {
            changes.push({
                field: 'model',
                current: currentMetadata.model,
                new: newMetadata.model
            });
            hasChanges = true;
        }

        // trends array changes (benign - no database file changes needed)
        const currentTrends = currentMetadata.trends || [];
        const newTrends = newMetadata.trends || [];

        // Compare trends arrays by converting to JSON strings for deep comparison
        const currentTrendsStr = JSON.stringify(currentTrends);
        const newTrendsStr = JSON.stringify(newTrends);

        if (currentTrendsStr !== newTrendsStr) {
            changes.push({
                field: 'trends',
                current: currentTrends,
                new: newTrends
            });
            hasChanges = true;
        }

        const metadataFields = ['label', 'description', 'longitude', 'latitude'];
        for (const field of metadataFields) {
            if (currentMetadata[field] !== newMetadata[field]) {
                changes.push({
                    field,
                    current: currentMetadata[field],
                    new: newMetadata[field]
                });
                hasChanges = true;
            }
        }

        // register changes
        if (changedDtypeRegisters.length > 0 || addedRegisters.length > 0 || removedRegisters.length > 0) {
            hasChanges = true;
        }

        if (changedDtypeRegisters.length > 0) {
            // case: 1+ dtype changed
            // strategy: create new table, delete old data, include +/- column changes in migration   
            migrationNeeded = true;
            migrationStrategy = 'RECREATE_TABLE';

            const allChanges = [
                ...changedDtypeRegisters.map(reg => ({
                    field: `register.${reg.register || reg.name}.dtype`,
                    type: 'dtype_changed',
                    current: currentRegMap.get(reg.register || reg.name).dtype,
                    new: reg.dtype
                }))
            ];

            // added and removed registers
            if (addedRegisters.length > 0) {
                allChanges.push(...addedRegisters.map(reg => ({
                    field: `register.${reg.register || reg.name}`,
                    type: 'added',
                    new: reg
                })));
            }

            if (removedRegisters.length > 0) {
                allChanges.push(...removedRegisters.map(reg => ({
                    field: `register.${reg.register || reg.name}`,
                    type: 'removed',
                    old: reg
                })));
            }

            return {
                type: 'INCOMPATIBLE',
                reason: 'Register datatype change',
                changes: allChanges,
                migrationNeeded,
                migrationStrategy,
                changedDtypeRegisters,
                // added and removed registers
                addedRegisters: addedRegisters.length > 0 ? addedRegisters : undefined,
                removedRegisters: removedRegisters.length > 0 ? removedRegisters : undefined,
                hasChanges
            };
        }
        else if (addedRegisters.length > 0) {
            // case: some signals have been added, remaining signals dtype unchanged
            // strategy: add columns in the database table
            migrationNeeded = true;
            migrationStrategy = 'ADD_COLUMNS';

            return {
                type: 'COMPATIBLE',
                reason: 'Signals added',
                changes: addedRegisters.map(reg => ({
                    field: `register.${reg.register || reg.name}`,
                    type: 'added',
                    new: reg
                })),
                migrationNeeded,
                migrationStrategy,
                addedRegisters,
                hasChanges
            };
        }
        else if (removedRegisters.length > 0) {
            // case: some signals have been removed, but we keep the columns
            // strategy: no migration needed, just log the changes
            return {
                type: 'COMPATIBLE',
                reason: 'Signals removed from metadata but columns preserved in database',
                changes: removedRegisters.map(reg => ({
                    field: `register.${reg.register || reg.name}`,
                    type: 'removed',
                    old: reg
                })),
                migrationNeeded: false, // no migration needed : we're keeping the columns
                removedRegisters,
                hasChanges
            };
        }

        // if we get here, only BENIGN changes
        return {
            type: changes.length > 0 ? 'COMPATIBLE' : 'BENIGN',
            changes,
            migrationNeeded: false,
            hasChanges
        };
    }

    async handleMetadataUpdate(mac, metadata, dataService) {
        console.log(`Handling metadata update for device ${mac}`);

        try {
            // current metadata and device info
            const currentMetadata = await this.getDeviceMetadata(mac);
            const device = await this.findDeviceByMac(mac);

            if (!device) {
                throw new Error(`Device not found: ${mac}`);
            }

            console.log(`Current metadata: ${JSON.stringify(currentMetadata)}`);

            // metadata comparison for compatibility
            const changes = await this.compareMetadata(currentMetadata, metadata);

            // log changes
            console.log(`Metadata comparison result: ${JSON.stringify(changes)}`);

            if (changes.changedDtypeRegisters && changes.changedDtypeRegisters.length > 0) {
                console.log(`Detected ${changes.changedDtypeRegisters.length} registers with dtype changes:`);
                changes.changedDtypeRegisters.forEach(reg => {
                    const regId = reg.register || reg.name;
                    const currentReg = currentMetadata?.registers?.find(r => (r.register || r.name) === regId);
                    console.log(`  - Register "${regId}": dtype changed from "${currentReg?.dtype}" to "${reg.dtype}"`);
                });
            }

            if (changes.addedRegisters && changes.addedRegisters.length > 0) {
                console.log(`Detected ${changes.addedRegisters.length} new registers:`);
                changes.addedRegisters.forEach(reg => {
                    console.log(`  - New register: "${reg.register || reg.name}" with dtype "${reg.dtype}"`);
                });
            }

            if (changes.removedRegisters && changes.removedRegisters.length > 0) {
                console.log(`Detected ${changes.removedRegisters.length} removed registers:`);
                changes.removedRegisters.forEach(reg => {
                    console.log(`  - Removed register: "${reg.register || reg.name}" with dtype "${reg.dtype}"`);
                });
            }

            if (changes.migrationNeeded && dataService) {
                console.log(`Data migration needed with strategy: ${changes.migrationStrategy}`);
                try {
                    await dataService.migrateDataTable(mac, currentMetadata, metadata, changes);
                    console.log('Data migration completed successfully');
                } catch (migrationError) {
                    console.error(`Error during data migration: ${migrationError.message}`);
                    // migration fails: throw an error to prevent metadata update
                    throw new Error(`Data migration failed: ${migrationError.message}`);
                }
            }
            // case : columns are removed but we don't drop them
            else if (changes.removedRegisters && changes.removedRegisters.length > 0) {
                console.log('Columns removed from metadata but preserved in database (no migration needed)');
                // still update the metadata even though we're not changing the database structure
            }

            // If compatible or migration successful, update the metadata
            if (changes.type !== 'INCOMPATIBLE' || (changes.migrationNeeded && changes.migrationStrategy)) {
                // comparison based version increment
                if (changes.hasChanges) {
                    console.log('Changes detected, updating device metadata with new version...');
                    const newVersion = device.meta_version + 1;
                    await this.updateDeviceMetadataWithVersion(mac, metadata, newVersion);
                    console.log(`Metadata updated successfully with new version: ${newVersion}`);
                } else {
                    console.log('No changes detected, updating metadata without incrementing version...');
                    await this.updateDeviceMetadataWithVersion(mac, metadata, device.meta_version);
                    console.log(`Metadata updated successfully, keeping version: ${device.meta_version}`);
                }
            } else {
                console.log('Incompatible metadata changes, not updating');
            }

            return changes;
        } catch (error) {
            console.error(`Error handling metadata update for device ${mac}:`, error);
            throw error;
        }
    }

    async getMetadataHistory(mac) {
        console.log(`Getting metadata history for device ${mac}`);

        try {
            const device = await this.findDeviceByMac(mac);
            if (!device) {
                throw new Error(`Device not found: ${mac}`);
            }

            const metaHistory = JSON.parse(device.meta_history || '[]');
            console.log(`Found ${metaHistory.length} metadata versions`);

            return metaHistory;
        } catch (error) {
            console.error(`Error getting metadata history for device ${mac}:`, error);
            throw error;
        }
    }
}
