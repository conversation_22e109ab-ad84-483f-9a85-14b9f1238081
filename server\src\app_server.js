import Fastify from 'fastify';
import knex from 'knex';
import knexconfig from '../knexfile.js';
import authPlugin from './plugins/authPlugin.js';
import corsPlugin from './plugins/corsPlugin.js';
import loginRoute from './routes/app/authentication/login.js';
import registerRoutes from './routes/app/authentication/register.js';
import appRoutes from './routes/app/appRoutes.js';
import db from './database/db.js';
import dbPlugin from './plugins/db.js';
import logger from './utils/logger.js';

import { config } from 'dotenv';

config();

const env = process.env.NODE_ENV || 'development';
const knexInstance = knex(knexconfig[env]);

const fastifyApp = Fastify({ logger: true });

// content-type parser for csv data
const addCSVParser = (fastify) => {
    fastify.addContentTypeParser('text/csv', { parseAs: 'string' }, (req, body, done) => {
        try {
            done(null, body);
        } catch (err) {
            done(err, undefined);
        }
    });
};
addCSVParser(fastifyApp);

const registerPlugins = async (fastify) => {
    await fastify.register(authPlugin);
    await fastify.register(corsPlugin);
    await fastify.register(dbPlugin);
    fastify.decorate('db', db);
};

await registerPlugins(fastifyApp);

await fastifyApp.register(loginRoute);
await fastifyApp.register(registerRoutes);
await fastifyApp.register(appRoutes, { prefix: '/api/app' });

logger.info(`App server Environment: ${env}`);

const startAppServer = async () => {
    const appPort = process.env.APP_PORT || 9000;
    try {
        await fastifyApp.listen({ port: appPort });
        logger.info(`App server listening on port ${appPort}`);
    } catch (err) {
        logger.error('Failed to start app server', { error: err.message });
        process.exit(1);
    }
};

startAppServer();