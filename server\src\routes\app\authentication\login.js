import db from '../../../database/db.js'
import bcrypt from 'bcryptjs'
import generateAuthResponse from '../../../utils/authResponse.js'

import { testRule, userRules } from '../../../../../shared/rules/validation.js'
import { createResponse } from '../../../utils/stdResponse.js'
import * as dotenv from 'dotenv'
dotenv.config()


export default async function loginRoutes(fastify, options) {
    fastify.post('/api/app/login', async (request, reply) => {

        const { data } = request.body || {};
        if (!data || typeof data !== 'object') {
            return reply.status(400).send({ error: 'Invalid request format' });
        }

        const { email = '', pass = '' } = data
        const errors = {};
        // Validate email and password
        const emailValidation = testRule(userRules.email, email);
        if (emailValidation !== true) errors.email = emailValidation;

        const passValidation = testRule(userRules.password, pass);
        if (passValidation !== true) errors.pass = passValidation;
        // If there are validation errors, return them in the standard response
        if (Object.keys(errors).length > 0) {
            return reply.status(200).send(createResponse({ errors }));
        }

        try {
            // user validation 
            const user = await db('users').where({ email: email.toLowerCase().trim() }).first()
            if (!user) {
                return reply.code(200).send(createResponse({
                    errors: { email: 'User not found' },
                }))
            }
            if (!user.registered) {
                return reply.code(403).send(createResponse({
                    errors: { email: 'Account not verified. Please verify your email.' },
                }))
            }

            //pwd validation 
            // const validPassword = await bcrypt.compare(pass, user.pass);     // hashing deactivated for testing
            const validPassword = pass === user.pass
            if (!validPassword) {
                return reply.status(200).send(
                    createResponse({
                        errors: { pass: 'Invalid password' },
                    })
                );
            }

            const { payload, extendedUser } = await generateAuthResponse(user)
            const token = fastify.jwt.sign(payload);


            return reply.code(200).send(createResponse({
                    data: extendedUser,
                    token,
                    other: { message: 'Login successful' },
                }))

        } catch (error) {
            fastify.log.error(error)
            return reply.code(500).send({ error: 'Internal server error' })
        }
    })
}


