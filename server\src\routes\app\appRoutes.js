// import { rotateJwtSecret } from '../utils/jwt-secret-gen.js'
import { createResponse } from '../../utils/stdResponse.js'
import { testRule, userRules } from '../../../../shared/rules/validation.js'
import authorizer from '../../utils/appAuthorizer.js'

export default async function appRoutes(fastify, options) {

    const { db, verifyJWT } = fastify

    // GET /api/app/users - Read all users
    fastify.get('/users', { preHandler: verifyJWT }, async (request, reply) => {

        const { page = 1, rowsPerPage = 10, sortBy = 'uid', descending = false, search = '' } = request.query
        const offset = (page - 1) * rowsPerPage
        const orderDirection = descending === 'true' || descending === true ? 'desc' : 'asc'

        try {
            let query = db('users')

            // search by first or last
            if (search) {
                query = query.where(function () {
                    this.where('first', 'like', `%${search}%`).orWhere('last', 'like', `%${search}%`)
                })
            }

            // get total count
            const total = await query.clone().count('* as count').first()
            const totalCount = parseInt(total.count, 10)

            const users = await query
                .select('*')
                .orderBy(sortBy, orderDirection)
                .limit(rowsPerPage)
                .offset(offset)

            const safeUsers = users.map(({ pass, ...user }) => user)

            reply.code(200).send(createResponse({
                data: safeUsers,
                other: {
                    pagination: {
                        total: totalCount,
                        page: parseInt(page, 10),
                        rowsPerPage: parseInt(rowsPerPage, 10)
                    }
                }
            }))
        } catch (error) {
            fastify.log.error(error)
            return reply.status(500).send(createResponse({ errors: ['Internal server error'] }))
        }
    })

    // GET /api/app/user/:uid - Read single user
    fastify.get('/user/:uid', { preHandler: verifyJWT }, async (request, reply) => {

        let { uid } = request.params
        const jwtPayload = request.user
        console.log('JWT Payload:', jwtPayload)  // Debug log

        if (!uid) {
            uid = jwtPayload.uid
            if (!uid) {
                return reply.code(400).send(createResponse({ errors: { uid: 'No userid Provided' } }))
            }
        }

        try {
            const user = await db('users').where({ uid: uid }).first()

            // fetch target user's organizations
            const orgRows = await db('users_organizations')
                .join('organizations', 'users_organizations.oid', 'organizations.oid')
                .where('users_organizations.uid', uid)
                .select(
                    'organizations.oid',
                    'organizations.name',
                    'organizations.description',
                    'users_organizations.role'
                )

            const organizations = orgRows.map(row => ({
                oid: row.oid,
                name: row.name,
                description: row.description,
                role: row.role
            }))

            const { pass, ...safeUser } = user

            const response = {
                ...safeUser,
                organizations
            }
            return reply.code(200).send(createResponse({ data: response }))


        } catch (error) {
            fastify.log.error(error)
            return reply.code(500).send(createResponse({ errors: { uid: 'Failed to retrieve user' } }))
        }
    })


    // POST /api/app/user/:uid - Update single user
    fastify.post('/user/:uid?', { preHandler: verifyJWT }, async (request, reply) => {
        const { uid } = request.params
        const { data: userData } = request.body || {}
        const jwtPayload = request.user

        if (!userData || typeof userData !== 'object') {
            return reply.code(200).send(createResponse({ errors: { data: 'Invalid request format: Expected "data" object' } }))
        }

        const errors = {}
        if (userData.first) {
            const firstValidation = testRule(userRules.name, userData.first)
            if (firstValidation !== true) errors.first = firstValidation
        }
        if (userData.last) {
            const lastValidation = testRule(userRules.name, userData.last)
            if (lastValidation !== true) errors.last = lastValidation
        }
        if (userData.email) {
            const emailValidation = testRule(userRules.email, userData.email)
            if (emailValidation !== true) errors.email = emailValidation
        }
        if (userData.pass) {
            const passValidation = testRule(userRules.password, userData.pass)
            if (passValidation !== true) errors.pass = passValidation
        }

        if (Object.keys(errors).length > 0) {
            return reply.status(200).send(createResponse({ errors }))
        }

        try {
            if (uid) {
                // update existing user
                const authResult = await authorizer(db, jwtPayload, 'modify', 'user', uid);
                if (authResult === -1) {
                    return reply.code(200).send(createResponse({ errors: { uid: 'User not found' } }))
                } else if (authResult === 0) {
                    return reply.code(403).send(createResponse({ errors: { uid: 'Forbidden: You do not have permission to modify this user' } }))
                }

                const updatedCount = await db('users').where({ uid }).update(userData)
                if (updatedCount === 0) {
                    return reply.status(200).send(createResponse({ errors: { uid: 'User not found' } }))
                }

                const updatedUser = await db('users').where({ uid }).first()
                const { pass, ...safeUser } = updatedUser
                return reply.code(200).send(createResponse({
                    data: safeUser,
                    other: { message: 'User updated successfully' }
                }))
            } else {
                // create new user (restricted to superusers)
                if (jwtPayload.admin !== 1) {
                    return reply.code(403).send(createResponse({ errors: { data: 'Forbidden: You do not have permission to create a user' } }))
                }

                const [newUserId] = await db('users').insert(userData).returning('uid')
                const newUser = await db('users').where({ uid: newUserId }).first()
                const { pass, ...safeUser } = newUser
                return reply.code(200).send(createResponse({
                    data: safeUser,
                    other: { message: 'User created successfully' }
                }))
            }
        } catch (error) {
            fastify.log.error(error)
            reply.code(500).send(createResponse({ errors: { data: 'Failed to process user data' } }))
        }
    })

    // DELETE /api/app/user/:uid - Delete single user
    fastify.delete('/user/:uid', { preHandler: verifyJWT }, async (request, reply) => {
        const { uid } = request.params
        const jwtPayload = request.user

        try {
            const authResult = await authorizer(db, jwtPayload, 'modify', 'user', uid);
            if (authResult === -1) {
                return reply.code(200).send(createResponse({ errors: { uid: 'User not found' } }))
            } else if (authResult === 0) {
                return reply.code(403).send(createResponse({ errors: { uid: 'Forbidden: You do not have permission to delete this user' } }))
            }

            const deletedCount = await db('users').where({ uid }).del()
            if (deletedCount === 0) {
                return reply.code(200).send(createResponse({ errors: { uid: 'User not found' } }))
            }
            return reply.code(200).send(createResponse({ other: { message: 'User deleted successfully', uid } }))
        } catch (error) {
            fastify.log.error(error)
            return reply.code(500).send(createResponse({ errors: { uid: 'Failed to delete user' } }))
        }
    })

    // GET /api/app/devices - Read all devices
    fastify.get('/devices', { preHandler: verifyJWT }, async (request, reply) => {

        const jwtPayload = request.user;
        const { page = 1, rowsPerPage = 10, sortBy = 'macid', descending = false, search = '' } = request.query;
        const offset = (page - 1) * rowsPerPage;
        const orderDirection = descending === 'true' || descending === true ? 'desc' : 'asc';

        try {
            let query = db('devices')
            if (jwtPayload.admin !== 1) {
                query = query.join('users_organizations', 'devices.oid', 'users_organizations.oid')
                    .where('users_organizations.uid', jwtPayload.uid)
                    .andWhere('users_organizations.role', '>=', 1)
            }

            if (search) {
                query = query.where(function () {
                    this.where('label', 'like', `%${search}%`).orWhere('description', 'like', `%${search}%`)
                })
            }

            const total = await query.clone().countDistinct('devices.macid as count').first()
            const totalCount = parseInt(total.count, 10)

            const devices = await query
                .select('devices.*')
                .orderBy(sortBy, orderDirection)
                .limit(rowsPerPage)
                .offset(offset)

            reply.code(200).send(createResponse({
                data: devices,
                other: {
                    pagination: {
                        total: totalCount,
                        page: parseInt(page, 10),
                        rowsPerPage: parseInt(rowsPerPage, 10)
                    }
                }
            }))
        } catch (error) {
            fastify.log.error(error)
            return reply.status(500).send(createResponse({ errors: ['Failed to retrieve devices'] }))
        }
    })

    // GET /api/app/device/:macid - Read single device
    fastify.get('/device/:macid', { preHandler: verifyJWT }, async (request, reply) => {

        const { macid } = request.params
        const jwtPayload = request.user
        try {
            const authResult = await authorizer(db, jwtPayload, 'read', 'device', macid);
            if (authResult === -1) {
                return reply.code(200).send(createResponse({ errors: { macid: 'Device not found' } }))
            } else if (authResult === 0) {
                return reply.code(403).send(createResponse({ errors: { macid: 'Forbidden: You do not have permission to access this device' } }))
            }

            const device = await db('devices').where({ macid }).first()
            return reply.code(200).send(createResponse({ data: device }))
        } catch (error) {
            fastify.log.error(error)
            reply.status(500).send(createResponse({ errors: { macid: 'Failed to retrieve device' } }))
        }
    })

    // POST /api/app/device/:macid - Update single device
    fastify.post('/device/:macid?', { preHandler: verifyJWT }, async (request, reply) => {
        const { macid } = request.params
        const { data: deviceData } = request.body || {}
        const jwtPayload = request.user

        if (!deviceData || typeof deviceData !== 'object') {
            return reply.status(400).send(createResponse({ errors: { data: 'Invalid request format: Expected "data" object' } }))
        }

        try {
            if (macid) {
                // Update existing device
                const authResult = await authorizer(db, jwtPayload, 'modify', 'device', macid);
                if (authResult === -1) {
                    return reply.code(200).send(createResponse({ errors: { macid: 'Device not found' } }))
                } else if (authResult === 0) {
                    return reply.code(403).send(createResponse({ errors: { macid: 'Forbidden: You do not have permission to modify this device' } }))
                }

                const updatedCount = await db('devices').where({ macid }).update(deviceData)
                if (updatedCount === 0) {
                    return reply.code(200).send(createResponse({ errors: { macid: 'Device not found' } }))
                }

                const updatedDevice = await db('devices').where({ macid }).first()
                return reply.code(200).send(createResponse({
                    data: updatedDevice,
                    other: { message: 'Device updated successfully' }
                }))
            } else {
                // Create new device (restricted to superusers)
                if (jwtPayload.admin !== 1) {
                    return reply.code(403).send(createResponse({ errors: { data: 'Forbidden: You do not have permission to create a device' } }))
                }

                // Validate MAC address presence
                if (!deviceData.mac) {
                    return reply.status(400).send(createResponse({ 
                        errors: { data: 'MAC address is required for device creation' }
                    }))
                }

                // Calculate macid from MAC address
                const cleanMac = deviceData.mac.replace(/:/g, '').toLowerCase();
                const calculatedMacid = parseInt(cleanMac, 16);

                // Add calculated macid to deviceData
                const deviceDataWithMacid = {
                    ...deviceData,
                    macid: calculatedMacid
                };

                const [newMacid] = await db('devices').insert(deviceDataWithMacid).returning('macid')
                const newDevice = await db('devices').where({ macid: newMacid }).first()
                return reply.code(200).send(createResponse({
                    data: newDevice,
                    other: { message: 'Device created successfully' }
                }))
            }
        } catch (error) {
            fastify.log.error(error)
            return reply.code(500).send(createResponse({ errors: { data: 'Failed to process device data' } }))
        }
    })

    //claim a device
    // POST /api/app/device/claim - Claim a device
    fastify.post('/device/claim', { preHandler: verifyJWT }, async (request, reply) => {
        const { macid, oid } = request.body.data || {};
        const jwtPayload = request.user;

        // Validate input
        if (!macid || !oid) {
            return reply.status(400).send(createResponse({ 
                errors: { data: 'Missing required fields: macid and oid' }
            }));
        }

        try {
            // check if user has permission to claim devices for this organization
            const authResult = await authorizer(db, jwtPayload, 'modify', 'organization', oid);
            if (authResult === -1) {
                return reply.code(404).send(createResponse({ 
                    errors: { oid: 'Organization not found' }
                }));
            } else if (authResult === 0) {
                return reply.code(403).send(createResponse({ 
                    errors: { oid: 'Forbidden: You do not have permission to claim devices for this organization' }
                }));
            }

            // check if device exists and is unclaimed
            const device = await db('devices')
                .where('macid', macid)
                .whereNull('oid')
                .first();

            if (!device) {
                return reply.code(404).send(createResponse({ 
                    errors: { macid: 'Device not found or already claimed' }
                }));
            }

            // claim the device (update with new oid and timestamp)
            const currentTime = Math.floor(Date.now() / 1000);
            const [updatedDevice] = await db('devices')
                .where('macid', macid)
                .whereNull('oid') 
                .update({
                    oid: oid,
                    utime: currentTime
                })
                .returning(['macid', 'mac', 'model', 'label', 'description', 'oid']);

            if (!updatedDevice) {
                return reply.code(409).send(createResponse({ 
                    errors: { macid: 'Device was claimed by another user' }
                }));
            }

            // return success response
            return reply.code(200).send(createResponse({
                data: updatedDevice,
                other: { message: 'Device claimed successfully' }
            }));

        } catch (error) {
            fastify.log.error(error);
            return reply.status(500).send(createResponse({ 
                errors: { data: 'Failed to process device claim' }
            }));
        }
    });   


    // =================================================================================================
    // GET /api/app/orgs - return orgs where the user has role of user in the org
    fastify.get('/orgs', { preHandler: verifyJWT }, async (request, reply) => {
        const { page = 1, rowsPerPage = 10, sortBy = 'organizations.oid', descending = false, search = '' } = request.query;
        const userId = request.user.uid;
        const offset = (page - 1) * rowsPerPage;
        const orderDirection = descending === 'true' || descending === true ? 'desc' : 'asc';

        try {
            let query = db('organizations')
                .join('users_organizations', 'organizations.oid', 'users_organizations.oid')
                .where('users_organizations.uid', userId)
                .andWhere('users_organizations.role', '>=', 0);

            if (search) {
                query = query.where(function () {
                    this.where('name', 'like', `%${search}%`)
                        .orWhere('description', 'like', `%${search}%`);
                });
            }

            const total = await query.clone().count('* as count').first();
            const totalCount = parseInt(total.count, 10);

            const orgs = await query
                .select('organizations.oid', 'organizations.name', 'organizations.description', 'organizations.email_domain')
                .orderBy(sortBy, orderDirection)
                .limit(rowsPerPage)
                .offset(offset);

            reply.code(200).send(createResponse({
                data: orgs,
                other: {
                    pagination: {
                        total: totalCount,
                        page: parseInt(page, 10),
                        rowsPerPage: parseInt(rowsPerPage, 10)
                    }
                }
            }));
        } catch (error) {
            fastify.log.error('Database Error:', error);
            return reply.status(500).send(createResponse({ errors: ['Failed to retrieve organizations'] }));
        }
    });

    // GET /api/app/org/:oid - Read single organization
    fastify.get('/org/:oid', { preHandler: verifyJWT }, async (request, reply) => {

        const { oid } = request.params
        const jwtPayload = request.user

        try {
            const authResult = await authorizer(db, jwtPayload, 'read', 'organization', oid);
            if (authResult === -1) {
                return reply.code(200).send(createResponse({ errors: { oid: 'Organization not found' } }))
            } else if (authResult === 0) {
                return reply.code(403).send(createResponse({ errors: { oid: 'Forbidden: You do not have permission to access this organization' } }))
            }

            const org = await db('organizations').where({ oid }).first()

            const users = await db('users_organizations')
                .join('users', 'users_organizations.uid', 'users.uid')
                .where('users_organizations.oid', oid)
                .select(
                    'users.uid',
                    'users.first',
                    'users.last',
                    'users.email',
                    'users.phone',
                    'users.enable',
                    'users.admin',
                    'users.registered',
                    'users_organizations.role'
                )

            const devices = await db('devices')
                .where({ oid })
                .select('macid', 'model', 'label', 'description', 'utime', 'ctime')

            const response = {
                ...org,
                users,
                devices
            }
            return reply.code(200).send(createResponse({ data: response }))
        } catch (error) {
            fastify.log.error(error)
            return reply.status(500).send(createResponse({ errors: { oid: 'Failed to retrieve organization' } }))
        }
    })

    // POST /api/app/org/:oid - Update single organization
    fastify.post('/org/:oid?', { preHandler: verifyJWT }, async (request, reply) => {

        const { oid } = request.params
        const { data: orgData } = request.body
        const jwtPayload = request.user

        if (!orgData || typeof orgData !== 'object') {
            return reply.status(400).send(createResponse({ errors: { data: 'Invalid request format: Expected "data" object' } }))
        }
        try {
            if (oid) {
                // update existing organization
                const authResult = await authorizer(db, jwtPayload, 'modify', 'organization', oid);
                if (authResult === -1) {
                    return reply.code(200).send(createResponse({ errors: { oid: 'Organization not found' } }))
                } else if (authResult === 0) {
                    return reply.code(403).send(createResponse({ errors: { oid: 'Forbidden: You do not have permission to modify this organization' } }))
                }

                const updatedCount = await db('organizations').where({ oid }).update(orgData)
                if (updatedCount === 0) {
                    return reply.code(404).send(createResponse({ errors: { oid: 'Organization not found' } }))
                }

                const updatedOrg = await db('organizations').where({ oid }).first()
                reply.code(200).send({
                    message: 'Organization updated successfully',
                    org: updatedOrg
                })
            } else {
                // create new organisation (restricted to superusers)
                if (jwtPayload.admin !== 1) {
                    return reply.code(403).send(createResponse({ errors: { data: 'Forbidden: You do not have permission to create an organization' } }))
                }

                const [newOid] = await db('organizations').insert(orgData).returning('oid')
                const newOrg = await db('organizations').where({ oid: newOid }).first()
                return reply.code(200).send(createResponse({
                    data: newOrg,
                    other: { message: 'Organization created successfully' }
                }))

            }
        } catch (error) {
            fastify.log.error(error)
            return reply.code(500).send(createResponse({ errors: { data: 'Failed to process organization data' } }))
        }

    })

    // DELETE /api/app/org/:oid - Delete single organization
    fastify.delete('/org/:oid', { preHandler: verifyJWT }, async (request, reply) => {

        const { oid } = request.params
        const jwtPayload = request.user

        try {
            const authResult = await authorizer(db, jwtPayload, 'modify', 'organization', oid);
            if (authResult === -1) {
                return reply.code(200).send(createResponse({ errors: { oid: 'Organization not found' } }))
            } else if (authResult === 0) {
                return reply.code(403).send(createResponse({ errors: { oid: 'Forbidden: You do not have permission to delete this organization' } }))
            }

            const deletedCount = await db('organizations').where({ oid }).del()
            if (deletedCount === 0) {
                return reply.code(404).send(createResponse({ errors: { oid: 'Organization not found' } }))
            }
            return reply.code(200).send(createResponse({
                other: { message: 'Organization deleted successfully', oid }
            }))
        } catch (error) {
            fastify.log.error(error)
            return reply.code(500).send(createResponse({ errors: { oid: 'Failed to delete organization' } }))
        }
    })


    // add users to organization
    // POST /api/app/org/:oid/users - Add users to organization, modify users role in an organisation : default role to 0
    fastify.post('/org/:oid/users',
        { preHandler: verifyJWT },
        async (request, reply) => {
            const { oid } = request.params;
            const { data } = request.body; // contain users array
            const jwtPayload = request.user;


            if (!data || !Array.isArray(data.users)) {
                return reply.status(400).send(createResponse({
                    errors: { data: 'Invalid request format: Expected "data.users" array' }
                }));
            }

            const users = data.users;

            const invalidUsers = users.filter(user =>
                !user || typeof user !== 'object' || !Number.isInteger(user.uid) || user.uid <= 0
            );
            if (invalidUsers.length > 0) {
                return reply.status(400).send(createResponse({
                    errors: { users: 'Each user must have a valid positive integer uid' }
                }));
            }

            try {
                // Authorization check
                const authResult = await authorizer(db, jwtPayload, 'modify', 'organization', oid);
                if (authResult === -1) {
                    return reply.code(404).send(createResponse({
                        errors: { oid: 'Organization not found' }
                    }));
                } else if (authResult === 0) {
                    return reply.code(403).send(createResponse({
                        errors: { oid: 'Forbidden: Insufficient permissions' }
                    }));
                }

                // Process users
                const inserted = [];
                const updated = [];
                const failed = [];

                for (const { uid, role = 0 } of users) {
                    try {
                        const existing = await db('users_organizations')
                            .where({ uid, oid })
                            .first();

                        if (existing) {     // if exists update, else insert new record
                            await db('users_organizations')
                                .where({ uid, oid })
                                .update({ role });
                            updated.push({ uid, role });
                        } else {
                            await db('users_organizations')
                                .insert({ uid, oid, role });
                            inserted.push({ uid, role });
                        }
                    } catch (err) {
                        if (err.code === 'SQLITE_CONSTRAINT') {
                            failed.push({
                                uid,
                                error: err.message.includes('FOREIGN KEY')
                                    ? 'User or organization does not exist'
                                    : 'Database constraint violation'
                            });
                        } else {
                            failed.push({ uid, error: 'Database operation failed' });
                        }
                    }
                }

                return reply.code(200).send(createResponse({
                    data: {
                        inserted_count: inserted.length,
                        updated_count: updated.length,
                        failed_count: failed.length,
                        failed_details: failed.length > 0 ? failed : undefined,
                        message: `Successfully processed ${inserted.length} insertions and ${updated.length} updates`
                    }
                }));

            } catch (error) {
                fastify.log.error(error);
                return reply.code(500).send(createResponse({
                    errors: {
                        system: 'Internal server error',
                        ...(process.env.NODE_ENV === 'development' && {
                            debug: error.message
                        })
                    }
                }));
            }
        }
    );

    // GET /api/app/devices/unclaimed - Search unclaimed devices by partial MAC
    fastify.get('/devices/unclaimed', { preHandler: verifyJWT }, async (request, reply) => {
        const { search = '' } = request.query;
        
        if (!search || search.length < 2) {
            return reply.status(400).send(createResponse({ 
                errors: { search: 'Search term must be at least 2 characters long' }
            }));
        }

        try {
            const devices = await db('devices')
                .whereNull('oid')
                .andWhere('mac', 'like', `%${search}%`)
                .select('macid', 'mac', 'model', 'label', 'description')
                .limit(5);  // limit for performance

            return reply.code(200).send(createResponse({
                data: devices,
                other: { message: `Found ${devices.length} unclaimed devices` }
            }));
        } catch (error) {
            fastify.log.error(error);
            return reply.status(500).send(createResponse({ 
                errors: { search: 'Failed to search for devices' }
            }));
        }
    });

}
