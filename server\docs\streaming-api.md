# Streaming API Documentation

## Query Data Endpoint with Streaming Support

The `/api/device/query-data` endpoint now supports streaming functionality for efficient data retrieval.

### Endpoint
```
GET /api/device/query-data
```

### Parameters

#### Required for Streaming Mode
- `device` (string): Single device MAC address to query
- `stream` (boolean): Set to `true` to enable streaming mode

#### Optional Parameters
- `from` (integer): Start timestamp (Unix timestamp)
- `to` (integer): End timestamp (Unix timestamp)
- `limit` (integer): Maximum number of records to return (default: 1000)
- `fields` (array): Array of field names to retrieve (not used in streaming mode)

#### Legacy Parameters (Non-streaming mode)
- `devices` (array): Array of device MAC addresses to query
- `format` (string): Response format ('json' or 'csv')

### Streaming Mode Response Format

When `stream=true`, the response returns data in the following format:

1. **First line**: Metadata array containing register information
2. **Subsequent lines**: Data arrays with timestamp and channel values
3. **Line separator**: Each array is separated by a newline character (`\n`)

#### Example Response

```
[{"index":1,"dtype":"F32","group":"AnalogIn","device":"AnalogIn","register":"GTS0","units":"°C","display":2},{"index":2,"dtype":"F32","group":"AnalogIn","device":"AnalogIn","register":"GTS1","units":"°C","display":2}]
[1747540500,13.27,13.09]
[1747540800,13.28,13.07]
[1747541100,13.29,13.08]
```

#### Metadata Array Structure
Each metadata object contains:
- `index`: 1-based register index
- `dtype`: Data type (e.g., "F32", "I32", "STR")
- `group`: Register group name
- `device`: Device type/name
- `register`: Register identifier
- `units`: Measurement units
- `display`: Display precision

#### Data Array Structure
Each data array contains:
- First element: Unix timestamp
- Subsequent elements: Channel values in register order (null for missing values)

### Usage Examples

#### Streaming Mode
```bash
# Stream data for a specific device
curl "http://localhost:3000/api/device/query-data?device=aa:bb:cc:dd:ee:ff&stream=true&from=1747540000&to=1747550000&limit=1000"
```

#### Legacy Mode (Multiple Devices)
```bash
# Query multiple devices (non-streaming)
curl "http://localhost:3000/api/device/query-data?devices[]=aa:bb:cc:dd:ee:ff&devices[]=11:22:33:44:55:66&from=1747540000&to=1747550000"
```

#### CSV Export
```bash
# Export data as CSV
curl "http://localhost:3000/api/device/query-data?device=aa:bb:cc:dd:ee:ff&format=csv&from=1747540000&to=1747550000"
```

### Response Headers

#### Streaming Mode
- `Content-Type: application/json`
- `Transfer-Encoding: chunked`

#### Legacy Mode
- `Content-Type: application/json` (for JSON format)
- `Content-Type: text/csv` (for CSV format)

### Error Responses

#### 400 Bad Request
- Missing required parameters
- Invalid parameter combinations

#### 404 Not Found
- Device not found
- No data table exists for device

#### 500 Internal Server Error
- Database connection failed
- Query execution error

### Implementation Details

The streaming functionality is implemented using:
- **Knex.js streaming**: Efficient database row streaming
- **Node.js streams**: Memory-efficient data processing
- **Fastify raw response**: Direct response writing for streaming

### Benefits of Streaming Mode

1. **Memory Efficiency**: Data is processed row-by-row instead of loading all data into memory
2. **Faster Response**: Client receives data as soon as it's available
3. **Scalability**: Can handle large datasets without memory constraints
4. **Real-time Feel**: Progressive data loading for better user experience

### Limitations

- Streaming mode supports only single device queries
- Field filtering is not implemented in streaming mode
- CSV format is not available in streaming mode
