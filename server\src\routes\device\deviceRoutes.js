import { <PERSON>ce<PERSON>ontroller } from '../../controllers/deviceController.js';
import { DeviceService } from '../../services/deviceService.js';
import { DataService } from '../../services/dataService.js';
import { StreamingService } from '../../services/streamingService.js';
import { TimeService } from '../../services/timeService.js';
import queryLogs from '../../utils/queryLogs.js';

export default async function deviceRoutes(fastify, options) {

    const knex = fastify.knex;

    const deviceService = new DeviceService(knex);
    const dataService = new DataService(knex, deviceService, fastify.log);
    const streamingService = new StreamingService(dataService, fastify.log);
    const timeService = new TimeService();

    const deviceController = new DeviceController(
        deviceService,
        dataService,
        timeService
    );

    // sending data (metadata or device data)
    fastify.post('/data', {
        handler: (request, reply) => {
            if (request.headers['dev-time']) {
                return deviceController.handleDevicePost(request, reply);
            } else {
                return deviceController.handleHandshake(request, reply);
            }
        }
    });

    // fetching metadata history>>> tested
    fastify.get('/meta-history/:mac', {
        handler: (request, reply) => deviceController.getMetadataHistory(request, reply)
    });

    // Query logs endpoint
    fastify.get('/logs', queryLogs);

    // Query device data from databases with streaming support
    fastify.post('/query-data', async (request, reply) => {
        try {
            const requestBody = request.body;
            const { from, to, limit = 1000, ...systems } = requestBody;

            // Validate systems object
            if (typeof systems !== 'object' || Object.keys(systems).length === 0) {
                return reply.code(400).send({ 
                    error: 'Invalid request',
                    message: 'At least one device MAC address is required'
                });
            }

            // Extract device list (MAC addresses) from object keys
            const deviceList = Object.keys(systems);

            // Parse parameters
            const parsedFrom = from && !isNaN(parseInt(from)) ? parseInt(from) : null;
            const parsedTo = to && !isNaN(parseInt(to)) ? parseInt(to) : null;
            const parsedLimit = limit && !isNaN(parseInt(limit)) ? parseInt(limit) : 1000;

            fastify.log.info(`POST /query-data - Devices: ${deviceList.length}, From: ${parsedFrom}, To: ${parsedTo}, Limit: ${parsedLimit}`);

            // Stream data from database
            await streamingService.streamMultipleDevices(deviceList, {
                deviceFields: systems, // Pass the systems object containing fields for each device
                from: parsedFrom,
                to: parsedTo,
                limit: parsedLimit,
            }, reply);

            return; // Response handled by streaming service

        } catch (error) {
            fastify.log.error('Error querying device data:', error);
            return reply.code(500).send({
                error: 'Failed to stream device data',
                message: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    });
}


