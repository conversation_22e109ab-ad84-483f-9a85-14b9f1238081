export class StreamingService {
    constructor(dataService, logger) {
        this.dataService = dataService;
        this.logger = logger;
    }

    /**
     * Stream data for multiple devices
     * @param {array} deviceMacs - Array of device MAC addresses
     * @param {object} options - Query options
     * @param {object} reply - Fastify reply object for streaming
     * @returns {Promise<void>}
     */
    async streamMultipleDevices(deviceMacs, options, reply) {
        try {
            reply.header('Content-Type', 'application/json; charset=UTF-8');
            reply.header('Transfer-Encoding', 'chunked');

            // Stream each device separately: metadata first, then data
            for (const mac of deviceMacs) {
                await this.streamDeviceData(mac, options, reply, false);
            }

            reply.raw.end();

        } catch (error) {
            this.logger.error('Error streaming multiple devices:', error);
            if (!reply.raw.headersSent) {
                reply.code(500).send({
                    error: 'Failed to stream device data',
                    message: error.message
                });
            } else {
                reply.raw.end();
            }
        }
    }

    /**
     * Stream device data in the expected format: metadata first, then data rows
     * @param {string} mac - Device MAC address
     * @param {object} options - Query options
     * @param {object} reply - Fastify reply object for streaming
     * @returns {Promise<void>}
     */
    async streamDeviceData(mac, options, reply, closeResponse = true) {
        try {
            const { deviceFields, from, to, limit } = options;

            this.logger.info(`Streaming data for device ${mac} from ${from} to ${to}`);

            // Set headers only for single device
            if (closeResponse) {
                reply.header('Content-Type', 'application/json; charset=UTF-8');
                reply.header('Transfer-Encoding', 'chunked');
            }

            // Get device metadata and validate
            const device = await this.dataService.deviceService.findDeviceByMac(mac);
            if (!device) {
                this.logger.warn(`Device not found: ${mac}`)
                if (closeResponse) reply.raw.end()
                return
            }

            // Get database connection
            let deviceKnex;
            try {
                deviceKnex = await this.dataService.getDeviceConnection(mac);
            } catch (connError) {
                this.logger.error(`Failed to connect to database for ${mac}:`, connError);
                if (closeResponse) reply.raw.end()
                return
            }

            // Check if data table exists
            const hasTable = await deviceKnex.schema.hasTable('data');
            if (!hasTable) {
                this.logger.warn(`Data table doesn't exist for device ${mac}`);
                if (closeResponse) reply.raw.end()
                return
            }

            // Parse metadata
            const metadata = await this.parseDeviceMetadata(device);
            const allRegisters = metadata.registers || [];

            // Get device-specific fields from deviceFields object
            const specificFields = this.getDeviceSpecificFields(mac, deviceFields);

            // Filter registers based on fields parameter
            const { filteredRegisters, selectedIndices } = this.filterRegistersByFields(allRegisters, specificFields);

            // Create system metadata in new format
            const systemMetadata = this.createSystemMetadata(device, filteredRegisters);

            // Send system metadata as single object (not array)
            reply.raw.write(JSON.stringify(systemMetadata) + '\n');

            // Build and execute streaming query with field filtering
            await this.executeStreamingQuery(deviceKnex, allRegisters, selectedIndices, { from, to, limit }, reply, closeResponse);

        } catch (error) {
            this.logger.error(`Error setting up stream for device ${mac}:`, error);
            if (!reply.sent) {
                reply.code(500).send({
                    error: 'Failed to stream device data',
                    message: error.message
                });
            }
        }
    }

    /**
     * Get device-specific fields from deviceFields object
     * @param {string} mac - Device MAC address
     * @param {object} deviceFields - Object containing fields for each device
     * @returns {array|null} - Array of field names for this device, or null for all fields
     */
    getDeviceSpecificFields(mac, deviceFields) {
        if (!deviceFields || typeof deviceFields !== 'object') {
            return null; // Return all fields if no deviceFields object
        }

        // Get group-based fields for this specific system
        const systemFields = deviceFields[mac];

        // If no fields specified for this system, return null (all fields)
        if (!systemFields || typeof systemFields !== 'object') {
            return null;
        }

        // Flatten all fields from all groups for this system
        const allFields = [];
        for (const [groupName, fields] of Object.entries(systemFields)) {
            if (Array.isArray(fields)) {
                allFields.push(...fields);
            } else {
                this.logger.warn(`Invalid field specification for group ${groupName} in device ${mac}:`, fields);
            }
        }

        // If no fields found, return null (all fields)
        if (allFields.length === 0) {
            return null;
        }

        return allFields;
    }

    /**
     * Create system metadata in the new format
     * @param {object} device - Device object
     * @param {array} registers - Filtered registers array
     * @returns {array} - An array of register metadata objects
     */
    createSystemMetadata(device, registers) {
        // Parse device metadata for basic info
        let deviceMeta = {};
        try {
            deviceMeta = typeof device.meta === 'string' ? JSON.parse(device.meta) : device.meta;
        } catch (e) {
            this.logger.error('Error parsing device metadata for basic info');
        }

        // Generate system identifier
        const systemId = this.generateSystemIdentifier(device, deviceMeta);

        // Group registers by device/group
        const groupedRegisters = this.groupRegistersByDevice(registers);

        // Create a flattened array of register details
        const allRegisterDetails = [];
        
        for (const [groupName, groupRegisters] of Object.entries(groupedRegisters)) {
            groupRegisters.forEach(reg => {
                // Find the original index of this register in the full filtered array
                const originalIndex = registers.findIndex(r => r === reg);
                
                allRegisterDetails.push({
                    index: originalIndex + 1,  // Start from 1 instead of 0
                    system: systemId,
                    mac: device.mac,
                    dtype: reg.dtype || 'unknown',
                    device: reg.device || reg.group || groupName,
                    register: reg.register || reg.name || `register_${originalIndex}`,
                    units: reg.units || '',
                    display: reg.display !== undefined ? reg.display : 0,
                });
            });
        }

        // Sort by index to ensure order is consistent
        allRegisterDetails.sort((a, b) => a.index - b.index);

        return allRegisterDetails;
    }

    /**
     * Generate a unique system identifier that's a valid JavaScript variable name
     * @param {object} device - Device object
     * @param {object} deviceMeta - Parsed device metadata
     * @returns {string} - System identifier (valid JS variable name)
     */
    generateSystemIdentifier(device, deviceMeta) {
        // Try to get a meaningful name from metadata
        let systemName = deviceMeta.label || deviceMeta.device || deviceMeta.model || deviceMeta.name;

        if (systemName) {
            systemName = systemName
                .replace(/[^a-zA-Z0-9_]/g, '_')  // Replace non-alphanumeric chars with underscore
                .replace(/^[0-9]/, '_$&')        // Prefix with underscore if starts with number
                .replace(/_+/g, '_');            // Replace multiple underscores with single
        } else {
            // Fallback: use last 6 chars of MAC
            const macSuffix = device.mac.replace(/:/g, '').slice(-6);
            systemName = `system_${macSuffix}`;
        }

        return systemName;
    }

    /**
     * Group registers by device type
     * @param {array} registers - Array of register objects
     * @returns {object} - Grouped registers by device type
     */
    groupRegistersByDevice(registers) {
        const groups = {};

        registers.forEach(register => {
            const deviceType = register.device || register.group || 'Unknown';

            if (!groups[deviceType]) {
                groups[deviceType] = [];
            }

            groups[deviceType].push(register);
        });

        return groups;
    }

    /**
     * Filter registers based on fields parameter with group support
     * @param {array} allRegisters - All available registers
     * @param {array|null} fields - Fields to filter by
     * @returns {object} - { filteredRegisters, selectedIndices }
     */
    filterRegistersByFields(allRegisters, fields) {
        // If no fields specified or fields is null/empty, return all registers
        if (!fields || !Array.isArray(fields) || fields.length === 0) {
            const allIndices = allRegisters.map((_, index) => index);
            return {
                filteredRegisters: allRegisters,
                selectedIndices: allIndices
            };
        }

        const filteredRegisters = [];
        const selectedIndices = [];

        // Filter registers based on field names
        fields.forEach(fieldName => {
            // Find register by name or index
            const registerIndex = allRegisters.findIndex(register => {
                const registerName = register.register || register.name || '';

                // Match by register name (case insensitive)
                if (registerName.toLowerCase() === fieldName.toLowerCase()) {
                    return true;
                }

                // Match by numeric index (if field is a number)
                if (!isNaN(fieldName) && parseInt(fieldName) === allRegisters.indexOf(register)) {
                    return true;
                }

                return false;
            });

            if (registerIndex !== -1) {
                // Avoid duplicates
                if (!selectedIndices.includes(registerIndex)) {
                    filteredRegisters.push(allRegisters[registerIndex]);
                    selectedIndices.push(registerIndex);
                }
            } else {
                this.logger.warn(`Field '${fieldName}' not found in device registers`);
            }
        });

        return { filteredRegisters, selectedIndices };
    }

    /**
     * Parse device metadata from device object
     * @param {object} device - Device object from database
     * @returns {object} - Parsed and cleaned metadata
     */
    async parseDeviceMetadata(device) {
        let metadata;
        try {
            metadata = typeof device.meta === 'string' ? JSON.parse(device.meta) : device.meta;
        } catch (e) {
            this.logger.error(`Error parsing device metadata: ${e.message}`);
            metadata = { registers: [] };
        }

        // Clean metadata using DataService method
        return this.dataService.cleanMetadata ?
            this.dataService.cleanMetadata(metadata) :
            metadata;
    }

    /**
     * Execute streaming query and write data to response
     * @param {object} deviceKnex - Knex database connection
     * @param {Array} registers - Array of register objects
     * @param {object} options - Query options (from, to, limit)
     * @param {object} reply - Fastify reply object
     * @returns {Promise<void>}
     */

    async executeStreamingQuery(deviceKnex, allRegisters, selectedIndices, options, reply, closeResponse = true) {
        const { from, to, limit } = options;

        // Build query with column selection
        let query = deviceKnex('data');

        // Select timestamp + specific columns based on selected indices
        const selectColumns = ['TA'];
        selectedIndices.forEach(index => {
            selectColumns.push(`D${index}`);
        });

        query = query.select(selectColumns).orderBy('TA', 'asc');

        // Apply timestamp filters only if provided and valid
        if (from !== null && from !== undefined && !isNaN(from)) {
            query = query.where('TA', '>=', from);
        }
        if (to !== null && to !== undefined && !isNaN(to)) {
            query = query.where('TA', '<=', to);
        }

        // Apply limit
        if (limit) {
            query = query.limit(limit);
        }

        this.logger.info(`Executing streaming query with columns [${selectColumns.join(', ')}]: ${query.toString()}`);

        return new Promise((resolve, reject) => {
            const stream = query.stream();

            stream.on('data', (row) => {
                try {
                    // Transform row to array format: [timestamp, selected_values...]
                    const dataArray = [row.TA];

                    // Add selected channel values in order
                    selectedIndices.forEach(index => {
                        const columnName = `D${index}`;
                        dataArray.push(row[columnName] !== undefined ? row[columnName] : null);
                    });

                    // Write data array as JSON followed by newline
                    reply.raw.write(JSON.stringify(dataArray) + '\n');
                } catch (writeError) {
                    this.logger.error('Error writing data row:', writeError);
                    stream.destroy();
                    reject(writeError);
                }
            });

            stream.on('end', () => {
                this.logger.info(`Finished streaming data`);
                if (closeResponse) {
                    reply.raw.end();
                }
                resolve();
            });

            stream.on('error', (error) => {
                this.logger.error(`Error streaming data:`, error);
                if (closeResponse) {
                    reply.raw.end();
                }
                reject(error);
            });
        });
    }

}
